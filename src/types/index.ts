// <PERSON><PERSON><PERSON> nghĩa các types cho ứng dụng

// Type cho trạng thái tiến trình
export type ProcessStatus = 'pending' | 'processing' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused' | 'reordered' | 'saving';
export type VoiceType = 'builtin' | 'finetuned' | 'imported' | 'web';

export interface Voice {
  id: string;
  name: string;
  createdAt: string;
  language: string;
  modelPath: string;
  configPath: string;
  vocabPath: string;
  referencePath: string[];
  previewPath?: string;
  description?: string;
  tags?: string[];
  temperature: number;
  repetitionPenalty: number;
  topK: number;
  topP: number;
  lengthPenalty: number;
  speed: number;
  type: VoiceType;
}

// Interface cho VoiceStore
export interface VoiceStoreInterface {
  voices: Voice[];
  defaultVoice?: string;
}

// Interface cho TTSProcessStore
export interface TTSProcessStoreInterface {
  processes: TTSProcess[];
}

// Logger types
export type LogLevel = 'debug' | 'info' | 'warning' | 'error';
export type LogChannel = 'system' | 'metadata' | 'finetune' | 'tts' | 'general' | 'tts-queue' | 'tts-process-store';
export type LogSource = 'node' | 'python' | 'renderer';

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  channel: LogChannel;
  message: string;
  data?: any;
  source: LogSource;
  processId?: string;
  type?: string;
}

export interface LoggerConfig {
  maxLogEntries: number;
  logToFile: boolean;
  logFilePath: string;
  logToConsole: boolean;
  minLevel: LogLevel;
}

export interface TTSChunk {
  id: string;
  text: string;
  status: ProcessStatus;
  audioPath?: string;
  duration?: number;
  error?: string;
}

export interface TTSProcess {
  id: string;
  text: string;
  voice: Voice;
  status: ProcessStatus;
  progress: number;
  createdAt: string;
  updatedAt: string;
  outputPath: string;
  fileName: string;
  error?: string;
  audioPath?: string;
  executionTime?: number;
  duration?: number;
  startTime?: string;
  reloadTime?: string;
  chunks: TTSChunk[];
  numThreads: number;
  numWorkers: number;
}

export interface AudioTextPair {
  audioPath: string;
  textPath: string;
  text: string;
  fileName: string;
  duration: number;
}

export interface MetadataItem {
  fileName: string;
  text: string;
  duration: number;
  segments?: number;
}

//
// export interface MetadataFileItem {
//   audio_file: string;
//   text: string;
//   speaker_name: string;
//   duration: number;
// }

export interface MetadataStats {
  totalFiles: number;
  totalDuration: number;
  trainCount: number;
  evalCount: number;
}

// Các loại message từ tiến trình metadata
export type MetadataMessageType = 'progress' | 'complete' | 'error' | 'info' | 'warning';

// Interface cho message từ tiến trình metadata
export interface MetadataMessage {
  type: MetadataMessageType;
  message?: string;
  data?: any;
  file?: string;
  duration?: number;
  segments?: number;
  text?: string;
  progress?: number;
  step?: number;
  total?: number;
  train_path?: string;
  eval_path?: string;
  train_count?: number;
  eval_count?: number;
  total_duration?: number;
  total_files?: number;
  metadata_items?: MetadataItem[];
  file_info?: {
    fileName: string;
    text: string;
    duration: number;
    segments?: number;
  };
}

// Các loại message từ tiến trình TTS
export type TTSMessageType =
    | 'log'           // Log thông thường
    | 'error'         // Log lỗi
    | 'tts_start'     // Bắt đầu xử lý TTS
    | 'tts_complete'  // Hoàn thành xử lý TTS
    | 'tts_failed'    // Xử lý TTS thất bại
    | 'audio_preview' // Preview âm thanh
    | 'progress';     // Tiến trình xử lý

// Interface cho message từ tiến trình TTS
export interface TTSMessage {
  type: TTSMessageType;
  message?: string;
  data?: any;
  text?: string;
  language?: string;
  output_path?: string;
  processing_time?: number;
  error?: string;
  traceback?: string;
  progress?: number;
}

// Interface cho cập nhật trạng thái
export interface ProcessStatusUpdate {
  processId: string;
  status: ProcessStatus;
  progress?: number;
  error?: string;
  audioPath?: string;
  updatedAt?: string;
  executionTime?: number;
  duration?: number;
  chunkInfo?: TTSChunk;
  chunks?: TTSChunk[]; // Thêm chunks vào interface
}

// Định nghĩa type cho electron API
export interface ElectronAPI {
  // Store operations
  getStoreValue: (key: string) => Promise<any>;
  setStoreValue: (key: string, value: any) => Promise<void>;

  // Dialog operations
  selectFolder: () => Promise<string | null>;
  selectVoiceZip: () => Promise<string | null>;
  selectExportPath: (defaultPath?: string) => Promise<string | null>;
  selectFiles: (options: {
    filters: { name: string; extensions: string[] }[];
    properties: ('openFile' | 'openDirectory' | 'multiSelections')[];
    maxFiles?: number;
  }) => Promise<string[]>;

  // File operations
  getAudioFiles: (folderPath: string) => Promise<AudioTextPair[]>;

  // Voice operations
  getVoices: () => Promise<Voice[]>;
  getVoice: (id: string) => Promise<Voice | null>;
  addVoice: (voice: Voice) => Promise<void>;
  updateVoice: (voice: Voice) => Promise<void>;
  deleteVoice: (id: string) => Promise<void>;
  setDefaultVoice: (id: string) => Promise<void>;
  getDefaultVoice: () => Promise<Voice | null>;
  importVoice: (zipPath: string) => Promise<void>;
  exportVoice: (voiceId: string, outputDir: string) => Promise<void>;

  // TTS operations
  textToSpeech(params: TextToSpeechParams): void;

  // Metadata operations
  processMetadata: (params: { processId: string, inputFolder: string, voiceName: string, language?: string }) => Promise<any>;

  // Finetune operations
  startFinetune: (params: { voiceName: string, trainMetadataPath?: string, evalMetadataPath?: string, epochs?: number, testText?: string }) => Promise<any>;
  cancelFinetune: (processId: string) => Promise<void>;
  getFinetuneStatus: (processId: string) => Promise<any>;

  // Open userData folder
  openUserDataFolder: () => Promise<boolean>;

  // Process status updates
  onUpdateProcess(callback: (update: TTSProcess) => void): () => void;

  // Toast notifications
  onToast(callback: (toast: { type: 'success' | 'error' | 'info' | 'warning'; message: string }) => void): () => void;

  showToast: (type: 'success' | 'error' | 'info' | 'warning', message: string) => void;

  // Metadata event listeners
  onMetadataMessage: (callback: (data: MetadataMessageData) => void) => () => void;
  onMetadataProgress: (callback: (data: MetadataProgressData) => void) => () => void;
  onMetadataComplete: (callback: (data: MetadataCompleteData) => void) => () => void;
  onMetadataError: (callback: (data: FinetuneErrorData) => void) => () => void;

  // TTS event listeners
  onTTSMessage: (callback: (data: { processId: string, message: TTSMessage }) => void) => () => void;
  onTTSLog: (callback: (data: { processId: string, log: string }) => void) => () => void;
  onTTSComplete: (callback: (data: { processId: string, outputPath: string }) => void) => () => void;
  onTTSError: (callback: (data: { processId: string, error: string }) => void) => () => void;

  // Finetune event listeners
  onFinetuneMessage: (callback: (data: FinetuneMessageData) => void) => () => void;
  onFinetuneProgress: (callback: (data: FinetuneProgressData) => void) => () => void;
  onFinetuneComplete: (callback: (data: FinetuneCompleteData) => void) => () => void;
  onFinetuneError: (callback: (data: FinetuneErrorData) => void) => () => void;

  // New methods
  getTTSProcesses: () => Promise<TTSProcess[]>;
  createProcess(process: TTSProcess): Promise<void>;
  updateProcess: (process: TTSProcess) => Promise<void>;
  removeProcess: (processId: string) => Promise<void>;
  cancelProcess: (processId: string) => Promise<void>;
  cleanupCompletedProcesses: () => Promise<TTSProcess[]>;
  isExistFile: (filePath: string) => Promise<boolean>;
  reloadChunk: (params: ReloadChunk) => Promise<{ error?: string }>;
  getAudioDuration: (audioPath: string, processId: string) => Promise<number>;
  reorderProcesses: (newOrder: string[]) => Promise<void>;
  selectOutputFolder: () => Promise<string | { error: string }>;
  selectReferenceFiles: () => Promise<string[] | { error: string }>;
  previewVoice: (voice: Voice) => Promise<string | { error: string }>;
}

export interface LoggerAPI {
  // Ghi log
  debug: (message: string, data?: any, channel?: LogChannel) => void;
  info: (message: string, data?: any, channel?: LogChannel) => void;
  warning: (message: string, data?: any, channel?: LogChannel) => void;
  error: (message: string, data?: any, channel?: LogChannel) => void;

  // Lấy logs
  getLogs: (filter?: {
    level?: LogLevel;
    channel?: LogChannel;
    source?: 'node' | 'python' | 'renderer';
    processId?: string;
    startTime?: string;
    endTime?: string;
  }) => Promise<LogEntry[]>;

  // Xóa logs
  clearLogs: () => Promise<boolean>;

  // Lấy cấu hình
  getConfig: () => Promise<LoggerConfig>;

  // Cập nhật cấu hình
  updateConfig: (config: Partial<LoggerConfig>) => Promise<boolean>;

  // Đăng ký lắng nghe log mới
  onNewLog: (callback: (log: LogEntry) => void) => () => void;
}

export interface TextToSpeechParams {
  text: string;
  voice: Voice;
  outputPath: string;
  processId: string;
  fileName: string;
  numThreads?: number;
  numWorkers?: number;
  language: string;
  chunkId?:string;
}

export interface ReloadChunk {
  text: string;
  processId: string;
  chunkId:string;
}

export interface MetadataMessageData {
  processId: string;
  message: MetadataMessage;
}

export interface MetadataProgressData {
  processId: string;
  fileName: string;
  text: string;
  duration: number;
  segments: number;
  totalFiles: number;
  totalSegments: number;
  totalDuration: number;
  progress: number;
}

export interface MetadataCompleteData {
  processId: string;
  trainPath: string;
  trainCount: number;
  evalPath: string;
  evalCount: number;
  totalDuration: number;
  totalFiles: number;
  progress: number;
}

export interface FinetuneErrorData {
  error: string;
}

export interface FinetuneMessageData {
  message: string;
}

export interface FinetuneProgressData {
  progress: number;
}

export interface FinetuneCompleteData {
  result: any;
}

export interface TTSProcessParams {
  id: string;
  text: string;
  voice: Voice;
  outputPath: string;
  language?: string;
}

export interface TTSQueueItem {
  processId: string;
  execute: () => Promise<void>;
  cancel?: () => Promise<void>;
  onError?: (error: any) => void;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
    logger: LoggerAPI;
  }
}
