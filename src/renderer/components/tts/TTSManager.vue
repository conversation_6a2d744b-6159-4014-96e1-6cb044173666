<script setup lang="ts">
import {ref, onMounted, onUnmounted} from 'vue';
import {useI18n} from 'vue-i18n';
import type {TTSProcess, TTSChunk} from '@types';
import Tooltip from '@renderer/components/common/Tooltip.vue';
import ConfirmModal from '@renderer/components/common/ConfirmModal.vue';
import {useToast} from '@renderer/composables/useToast';
import {useAudioPlayer} from '@renderer/composables/useAudioPlayer';
import {useProcesses} from '@renderer/composables/useProcesses';
import * as Formatter from '@renderer/utils/formatters';

const {t} = useI18n();
const {showToast} = useToast();
const {toggleAudio} = useAudioPlayer();
const {
  processes,
  expandedProcesses,
  cleanupProcesses,
  toggleProcessDetails,
  loadProcesses,
  onUpdateProcess,
  cancelProcess,
  deleteProcess,
  reorderProcesses,
} = useProcesses();

// State
const showCleanupModal = ref(false);
const confirmTitle = ref('');
const confirmMessage = ref('');
const confirmAction = ref<'cleanup' | 'cancel' | 'delete' | null>(null);
const pendingProcess = ref<TTSProcess | null>(null);
const playingProcessId = ref<string | null>(null);
const playingChunkId = ref<string | null>(null);
const currentTime = ref(Date.now());
const editingChunk = ref<{processId: string, chunkId: string, text: string} | null>(null);
const reloadingChunks = ref<Set<string>>(new Set());
const draggedProcess = ref<TTSProcess | null>(null);
const dropTarget = ref<{process: TTSProcess, position: 'before' | 'after'} | null>(null);

// Cập nhật thời gian hiện tại mỗi giây
let timeInterval: NodeJS.Timeout | null = null;

// Methods
const getStatusClass = (status: TTSProcess['status']) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    processing: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    running: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-400',
    completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    failed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    cancelled: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
    error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
  };
  return classes[status];
};

const formatText = (text: string) => {
  if (text.length <= 20) return text;
  return text.substring(0, 10) + '...' + text.substring(text.length - 10);
};

const formatExecutionTime = (process: TTSProcess) => {
  let executionTime = process.executionTime;

  // Nếu process đang chạy (processing hoặc running) và có startTime, tính thời gian thực
  if ((process.status === 'processing' || process.status === 'running') && process.startTime && !executionTime) {
    executionTime = currentTime.value - new Date(process.startTime).getTime();
  }

  if (!executionTime) return '-';

  const totalSeconds = Math.floor(executionTime / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  if (hours > 0) {
    // Định dạng HH:mm:SS khi vượt quá 1 tiếng
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    // Định dạng mm:SS khi dưới 1 tiếng
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
};

const formatDuration = (duration: number | null | undefined) => {
  if (!duration || duration <= 0) return '-';
  return Formatter.formatDuration(duration);
};

const playAudio = async (process: TTSProcess) => {
  try {
    if (!process.audioPath) {
      showToast('error', t('home.process.errors.noOutputPath'));
      return;
    }

    await toggleAudio(process.audioPath, () => {
      playingProcessId.value = null;
    });
    playingProcessId.value = playingProcessId.value === process.id ? null : process.id;
  } catch (error) {
    console.error('Error playing audio:', error);
    showToast('error', String(error));
  }
};

const playChunkAudio = async (chunk: TTSChunk) => {
  try {
    if (!chunk.audioPath) {
      showToast('error', t('home.process.errors.noOutputPath'));
      return;
    }

    await toggleAudio(chunk.audioPath, () => {
      playingChunkId.value = null;
    });
    playingChunkId.value = playingChunkId.value === chunk.id ? null : chunk.id;
  } catch (error) {
    console.error('Error playing chunk audio:', error);
    showToast('error', String(error));
  }
};

const handleCleanup = () => {
  confirmTitle.value = t('home.process.confirm.cleanup.title');
  confirmMessage.value = t('home.process.confirm.cleanup.message');
  confirmAction.value = 'cleanup';
  showCleanupModal.value = true;
};

const handleCancel = (process: TTSProcess) => {
  confirmTitle.value = t('home.process.confirm.cancel.title');
  confirmMessage.value = t('home.process.confirm.cancel.message');
  confirmAction.value = 'cancel';
  pendingProcess.value = process;
  showCleanupModal.value = true;
};

const handleDelete = (process: TTSProcess) => {
  confirmTitle.value = t('home.process.confirm.delete.title');
  confirmMessage.value = t('home.process.confirm.delete.message');
  confirmAction.value = 'delete';
  pendingProcess.value = process;
  showCleanupModal.value = true;
};

const handleConfirm = async () => {
  try {
    if (!pendingProcess.value && confirmAction.value !== 'cleanup') return;

    if (confirmAction.value === 'cleanup') {
      await cleanupProcesses();
    } else if (confirmAction.value === 'cancel') {
      await cancelProcess(pendingProcess.value!.id);
    } else if (confirmAction.value === 'delete') {
      await deleteProcess(pendingProcess.value!.id);
    }
  } catch (error) {
    console.error('Error handling confirm action:', error);
    showToast('error', t('common.error'));
  } finally {
    showCleanupModal.value = false;
    confirmAction.value = null;
    pendingProcess.value = null;
  }
};

const toggleDetails = (process: TTSProcess) => {
  toggleProcessDetails(process.id);
};

// Chunk editing methods
const startEditChunk = (processId: string, chunkId: string, currentText: string) => {
  editingChunk.value = {
    processId,
    chunkId,
    text: currentText
  };
};

const cancelEditChunk = () => {
  editingChunk.value = null;
};

const saveEditChunk = async () => {
  if (!editingChunk.value) return;

  const { processId, chunkId, text } = editingChunk.value;

  try {
    // Tìm process và voice
    const process = processes.value.find(p => p.id === processId);
    if (!process) {
      showToast('error', 'Không tìm thấy process');
      return;
    }

    // Đánh dấu chunk đang reload
    reloadingChunks.value.add(chunkId);

    // Gọi API reload chunk
    const result = await window.electronAPI.reloadChunk({
      processId,
      chunkId,
      text: text.trim()
    });

    if (result.error) {
      showToast('error', `Lỗi reload chunk: ${result.error}`);
    } else {
      showToast('success', 'Đã reload chunk thành công');
      // Reload processes để cập nhật UI
      await loadProcesses();
    }
  } catch (error) {
    console.error('Error reloading chunk:', error);
    showToast('error', `Lỗi reload chunk: ${String(error)}`);
  } finally {
    reloadingChunks.value.delete(chunkId);
    editingChunk.value = null;
  }
};

const isChunkReloading = (chunkId: string) => {
  return reloadingChunks.value.has(chunkId);
};

const isChunkEditing = (chunkId: string) => {
  return editingChunk.value?.chunkId === chunkId;
};

// Drag and drop handlers
const handleDragStart = (process: TTSProcess, event: DragEvent) => {
  console.log(`[Drag Start] Process: ${process.fileName} (${process.id})`);
  draggedProcess.value = process;
  
  // Thêm hiệu ứng cho trình duyệt để biết đây là thao tác 'move'
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move';
  }
};

const handleDragOver = (e: DragEvent, targetProcess: TTSProcess) => {
  e.preventDefault();
  if (!draggedProcess.value || draggedProcess.value.id === targetProcess.id) {
    return;
  }

  const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
  const percent = (e.clientY - rect.top) / rect.height;

  // Xác định vị trí 'before' hoặc 'after' dựa trên 50% chiều cao
  // Sử dụng 50% là đủ, dead-zone có thể không cần thiết khi UI đã mượt mà
  const newPosition = percent < 0.5 ? 'before' : 'after';

  // Chỉ cập nhật state nếu có sự thay đổi để tránh trigger re-render không cần thiết
  if (!dropTarget.value || dropTarget.value.process.id !== targetProcess.id || dropTarget.value.position !== newPosition) {
    console.log(`[Drag Over] Target: ${targetProcess.fileName}, Position: ${newPosition}`);
    dropTarget.value = {
      process: targetProcess,
      position: newPosition
    };
  }
};

const handleDragLeave = (e: DragEvent) => {
  // Chỉ reset khi con trỏ thực sự rời khỏi phần tử, không phải khi di chuyển giữa các phần tử con
  const relatedTarget = e.relatedTarget as Node | null;
    if (!(e.currentTarget as Node).contains(relatedTarget)) {
        if (dropTarget.value) {
            console.log('[Drag Leave] Resetting drop target');
            dropTarget.value = null;
        }
    }
};

const handleDrop = async (e: DragEvent) => {
  e.preventDefault();

  // *** THAY ĐỔI QUAN TRỌNG NHẤT ***
  // Logic thả bây giờ hoàn toàn dựa vào state 'dropTarget', không tính toán lại
  if (!draggedProcess.value || !dropTarget.value) {
    console.log('[Drop] Invalid drop condition. Resetting state.');
    draggedProcess.value = null;
    dropTarget.value = null;
    return;
  }
  
  const { process: targetProcess, position: dropPosition } = dropTarget.value;

  // Không cho phép thả vào chính nó
  if (draggedProcess.value.id === targetProcess.id) {
    console.log('[Drop] Dropped on self. Resetting state.');
    draggedProcess.value = null;
    dropTarget.value = null;
    return;
  }

  try {
    console.log(`[Drop Start] Dragged: ${draggedProcess.value.fileName}`);
    console.log(`[Drop Start] Target: ${targetProcess.fileName}, Position: ${dropPosition}`);

    const currentOrder = processes.value.map(p => p.id);
    const newOrder = [...currentOrder];
    
    const draggedId = draggedProcess.value.id;
    const targetId = targetProcess.id;

    // Xóa item đang kéo ra khỏi vị trí cũ
    const draggedIndex = newOrder.findIndex(id => id === draggedId);
    if (draggedIndex > -1) {
      newOrder.splice(draggedIndex, 1);
    }
    
    // Tìm vị trí mới để chèn vào
    let targetIndex = newOrder.findIndex(id => id === targetId);

    // Chèn item vào trước hoặc sau target
    if (dropPosition === 'before') {
      newOrder.splice(targetIndex, 0, draggedId);
    } else { // 'after'
      newOrder.splice(targetIndex + 1, 0, draggedId);
    }

    console.log('[Drop] New order:', newOrder);

    // Gửi yêu cầu cập nhật thứ tự
    await reorderProcesses(newOrder);
    
    console.log('[Drop Complete] Process order updated successfully');
    showToast('success', t('home.process.reorder.success'));

  } catch (error) {
    console.error('[Drop Error] Failed to reorder processes:', error);
    showToast('error', t('home.process.reorder.error'));
  } finally {
    console.log('[Drop End] Resetting drag state');
    draggedProcess.value = null;
    dropTarget.value = null;
  }
};

// Đăng ký các event listeners
let unsubscribeProcessStatus: (() => void) | null = null;

onMounted(async () => {
  await loadProcesses();

  if (window.electronAPI) {
    unsubscribeProcessStatus = window.electronAPI.onUpdateProcess((process) => {
      onUpdateProcess(process);
    });
  }

  // Khởi tạo interval để cập nhật thời gian hiện tại
  timeInterval = setInterval(() => {
    currentTime.value = Date.now();
  }, 1000);
});

onUnmounted(() => {
  // Hủy đăng ký event listeners
  if (unsubscribeProcessStatus) unsubscribeProcessStatus();

  // Dọn dẹp interval
  if (timeInterval) {
    clearInterval(timeInterval);
    timeInterval = null;
  }
});
</script>

<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-white">{{ t('home.process.title') }}</h2>
      <button
          @click="handleCleanup"
          class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
                d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                clip-rule="evenodd"/>
        </svg>
        {{ t('home.process.actions.cleanup') }}
      </button>
    </div>

    <div v-if="processes.length === 0" class="text-center py-8">
      <p class="text-gray-500 dark:text-gray-400">{{ t('home.process.empty') }}</p>
    </div>

    <div v-else class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-700">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
            {{ t('home.process.columns.fileName') }}
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
            {{ t('home.process.columns.text') }}
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
            {{ t('home.process.columns.voice') }}
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
            {{ t('home.process.columns.status') }}
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
            {{ t('home.process.columns.executionTime') }}
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
            {{ t('home.process.columns.duration') }}
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
            {{ t('home.process.columns.actions') }}
          </th>
        </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200 dark:divide-gray-700 dark:bg-gray-800">
        <template v-for="process in processes" :key="process.id">
          <!-- Process row -->
          <tr 
            class="hover:bg-gray-50 dark:hover:bg-gray-700 cursor-move transition-all duration-200"
            :class="{
              'opacity-50': draggedProcess?.id === process.id,
              // Dùng shadow để giả lập border-top 3px, màu blue-500
              '[box-shadow:inset_0_20px_0_0_#3b82f6]': dropTarget?.process.id === process.id && dropTarget.position === 'before',
              // Dùng shadow để giả lập border-bottom 3px, màu blue-500
              '[box-shadow:inset_0_-20px_0_0_#3b82f6]': dropTarget?.process.id === process.id && dropTarget.position === 'after',
            }"
            draggable="true"
            @dragstart="handleDragStart(process, $event)"
            @dragover.prevent="handleDragOver($event, process)"
            @dragleave="handleDragLeave($event)"
            @drop="handleDrop($event)"
            @dragend="draggedProcess = null; dropTarget = null"
          >
            <td class="px-6 py-4 whitespace-wrap">
              <span class="text-sm text-gray-900 dark:text-white">{{ process.fileName }}</span>
            </td>
            <td class="px-6 py-4">
              <Tooltip>
                <template #trigger>
                  <span class="text-sm text-gray-900 dark:text-white">{{ formatText(process.text) }}</span>
                </template>
                <span>{{ process.text }}</span>
              </Tooltip>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="text-sm text-gray-900 dark:text-white">{{ process.voice.name }}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div v-if="process.status === 'processing' || process.status === 'running'" class="space-y-1">
                <span :class="getStatusClass(process.status)" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                  {{ t(`home.process.status.${process.status}`) }}{{ process.progress > 0 ? ` ${process.progress}%` : '' }}
                </span>
              </div>
              <span v-else :class="getStatusClass(process.status)" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                {{ t(`home.process.status.${process.status}`) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-end">
              <span class="text-sm text-gray-900 dark:text-white pr-6">{{ formatExecutionTime(process) }}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-end">
              <span class="text-sm text-gray-900 dark:text-white pr-6">{{ formatDuration(process.duration) }}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex justify-end space-x-2">
                <button
                    v-if="process.status === 'completed' && process.audioPath"
                    @click="playAudio(process)"
                    class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                          clip-rule="evenodd"/>
                  </svg>
                </button>
                <button
                    v-if="process.status === 'processing' || process.status === 'running'"
                    @click="handleCancel(process)"
                    class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z"
                          clip-rule="evenodd"/>
                  </svg>
                </button>
                <button
                    @click="handleDelete(process)"
                    class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                          d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                          clip-rule="evenodd"/>
                  </svg>
                </button>
                <button
                    @click="toggleDetails(process)"
                    class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" :class="{ 'transform rotate-180': expandedProcesses.has(process.id) }"
                       viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                          clip-rule="evenodd"/>
                  </svg>
                </button>
              </div>
            </td>
          </tr>

          <!-- Chi tiết chunks khi mở rộng -->
          <tr v-if="expandedProcesses.has(process.id)" class="bg-gray-50 dark:bg-gray-700">
            <td colspan="7" class="px-6 py-4">
              <div class="space-y-2">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">{{ t('home.process.chunks.title', { count: process.chunks?.length || 0 }) }}</h4>

                <div v-if="process.chunks && process.chunks.length > 0" class="space-y-2">
                  <div v-for="(chunk, index) in process.chunks" :key="chunk.id"
                       class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                    <div class="flex items-center justify-between mb-2">
                      <div class="flex items-center space-x-2">
                        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">{{ t('home.process.chunks.chunk', { index: index + 1 }) }}</span>
                        <span :class="getStatusClass(chunk.status)" class="px-2 py-1 text-xs font-semibold rounded-full">
                          {{ t(`home.process.status.${chunk.status}`) }}
                        </span>
                      </div>
                    </div>

                    <!-- Text của chunk -->
                    <div class="text-sm text-gray-700 dark:text-gray-300 mb-2">
                      <div v-if="!isChunkEditing(chunk.id)" class="flex items-start justify-between">
                        <span class="break-words flex-1">{{ chunk.text }}</span>
                        <button
                            v-if="chunk.status === 'completed' || chunk.status === 'failed'"
                            @click="startEditChunk(process.id, chunk.id, chunk.text)"
                            class="ml-2 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 flex-shrink-0"
                            :title="chunk.status === 'failed' ? t('home.process.chunks.edit.retry') : t('home.process.chunks.edit.button')"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z" />
                          </svg>
                        </button>
                      </div>
                      <div v-else class="space-y-2">
                        <textarea
                            v-model="editingChunk!.text"
                            class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none"
                            rows="3"
                            :placeholder="t('home.process.chunks.edit.placeholder')"
                            :disabled="isChunkReloading(chunk.id)"
                        ></textarea>
                        <div class="flex space-x-2">
                          <button
                              @click="saveEditChunk"
                              :disabled="isChunkReloading(chunk.id)"
                              class="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                          >
                            <span v-if="isChunkReloading(chunk.id)">{{ t('home.process.chunks.edit.reloading') }}</span>
                            <span v-else>{{ t('home.process.chunks.edit.save') }}</span>
                          </button>
                          <button
                              @click="cancelEditChunk"
                              :disabled="isChunkReloading(chunk.id)"
                              class="px-3 py-1 bg-gray-500 text-white rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                          >
                            {{ t('home.process.chunks.edit.cancel') }}
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- Thông tin bổ sung -->
                    <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                      <span v-if="chunk.duration">{{ t('home.process.chunks.info.duration') }}: {{ formatDuration(chunk.duration) }}</span>
                      <span v-if="chunk.audioPath">{{ t('home.process.chunks.info.audio') }}: {{ chunk.audioPath }}</span>
                      <button
                          v-if="chunk.status === 'completed' && chunk.audioPath"
                          @click="playChunkAudio(chunk)"
                          class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                                clip-rule="evenodd"/>
                        </svg>
                      </button>
                    </div>
                    <p v-if="chunk.error" class="text-red-500 text-sm">{{ chunk.error }}</p>
                  </div>
                </div>
                <div v-else class="text-sm text-gray-500 dark:text-gray-400">
                  {{ t('home.process.chunks.empty') }}
                </div>
              </div>
            </td>
          </tr>
        </template>
        </tbody>
      </table>
    </div>

    <ConfirmModal
        v-model:show="showCleanupModal"
        :title="confirmTitle"
        :message="confirmMessage"
        :showCancel="true"
        @confirm="handleConfirm"
        @close="showCleanupModal = false; confirmAction = null; pendingProcess = null;"
    />
  </div>
</template>

<style scoped>
.cursor-move {
  cursor: move;
}

/* Add smooth transitions */
tr {
  transition: all 0.2s ease-in-out;
}

/* Add drop zone animation */
@keyframes dropZonePulse {
  0% { height: 0; }
  50% { height: 2rem; }
  100% { height: 2rem; }
}

tr[class*="bg-blue-"] {
  animation: dropZonePulse 0.2s ease-in-out;
}
</style>
