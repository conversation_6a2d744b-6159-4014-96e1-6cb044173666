<script setup lang="ts">
import { ref, watch } from 'vue';
import type { Voice } from '@types';
import { useI18n } from 'vue-i18n';
import { useToast } from '../../composables/useToast';
import ConfirmModal from '../common/ConfirmModal.vue';

const props = defineProps<{
  show: boolean;
  voice: Voice;
}>();

const emit = defineEmits<{
  (e: 'update:show', value: boolean): void;
  (e: 'update', voice: Voice): void;
  (e: 'delete', voiceId: string): void;
}>();

const { t } = useI18n();
const { showToast } = useToast();
const isLoading = ref(false);
const showDeleteConfirm = ref(false);

const formData = ref<Partial<Voice>>({
  name: '',
  description: '',
  language: 'en',
  tags: [],
  speed: 0,
  temperature: 0.5,
  repetitionPenalty: 1.0,
  topK: 25,
  topP: 0.65,
  lengthPenalty: 1.0,
  referencePath: [],
  modelPath: '',
  configPath: '',
  vocabPath: '',
  previewPath: ''
});

// Cập nhật formData khi voice thay đổi
watch(() => props.voice, (newVoice) => {
  if (newVoice) {
    formData.value = { ...newVoice };
  }
}, { immediate: true });

// Hàm xử lý chọn file
const handleSelectFile = async (type: 'model' | 'config' | 'vocab' | 'preview') => {
  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }

    let filters;
    switch (type) {
      case 'model':
        filters = [{ name: 'Model Files', extensions: ['pth'] }];
        break;
      case 'config':
      case 'vocab':
        filters = [{ name: 'JSON Files', extensions: ['json'] }];
        break;
      case 'preview':
        filters = [{ name: 'Audio Files', extensions: ['wav', 'mp3', 'flac'] }];
        break;
    }

    const files = await window.electronAPI.selectFiles({
      filters,
      properties: ['openFile']
    });

    if (files && files.length > 0) {
      const file = files[0];
      const ext = file.split('.').pop()?.toLowerCase();

      // Kiểm tra định dạng file
      let isValid = false;
      switch (type) {
        case 'model':
          isValid = ext === 'pth';
          break;
        case 'config':
        case 'vocab':
          isValid = ext === 'json';
          break;
        case 'preview':
          isValid = ['wav', 'mp3', 'flac'].includes(ext || '');
          break;
      }

      if (!isValid) {
        showToast('error', t('voices.update.error.invalidFileType'));
        return;
      }

      switch (type) {
        case 'model':
          formData.value.modelPath = file;
          break;
        case 'config':
          formData.value.configPath = file;
          break;
        case 'vocab':
          formData.value.vocabPath = file;
          break;
        case 'preview':
          formData.value.previewPath = file;
          break;
      }
    }
  } catch (error) {
    console.error('Lỗi khi chọn file:', error);
    showToast('error', t('voices.update.error.selectFile'));
  }
};

// Hàm xử lý thêm reference file
const handleAddReference = async () => {
  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }

    if (formData.value.referencePath && formData.value.referencePath.length >= 5) {
      showToast('error', t('voices.update.reference.maxLimit'));
      return;
    }

    const files = await window.electronAPI.selectFiles({
      filters: [{ name: 'Audio Files', extensions: ['wav', 'mp3', 'flac'] }],
      properties: ['openFile']
    });

    if (files && files.length > 0) {
      const file = files[0];
      const ext = file.split('.').pop()?.toLowerCase();
      
      // Kiểm tra định dạng file
      if (!['wav', 'mp3', 'flac'].includes(ext || '')) {
        showToast('error', t('voices.update.error.invalidFileType'));
        return;
      }

      if (!formData.value.referencePath) {
        formData.value.referencePath = [];
      }
      formData.value.referencePath.push(file);
    }
  } catch (error) {
    console.error('Lỗi khi thêm reference file:', error);
    showToast('error', t('voices.update.reference.error.add'));
  }
};

// Hàm xử lý xóa reference file
const handleRemoveReference = (index: number) => {
  if (formData.value.referencePath) {
    formData.value.referencePath.splice(index, 1);
  }
};

// Hàm xử lý xóa voice
const handleDelete = async () => {
  showDeleteConfirm.value = true;
};

// Hàm xác nhận xóa voice
const confirmDelete = async () => {
  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }

    isLoading.value = true;
    await window.electronAPI.deleteVoice(props.voice.id);
    emit('delete', props.voice.id);
    emit('update:show', false);
    showToast('success', t('voices.manage.actions.deleteSuccess'));
  } catch (error) {
    console.error('Lỗi khi xóa voice:', error);
    showToast('error', t('voices.manage.actions.deleteError'));
  } finally {
    isLoading.value = false;
    showDeleteConfirm.value = false;
  }
};

// Hàm xử lý cập nhật voice
const handleUpdate = async () => {
  try {
    if (!window.electronAPI) {
      console.error('electronAPI không khả dụng');
      return;
    }

    // Kiểm tra các trường bắt buộc
    if (!formData.value.name?.trim()) {
      showToast('error', t('voices.update.error.nameRequired'));
      return;
    }

    isLoading.value = true;

    // Tạo object mới với các thuộc tính cần thiết
    const updatingVoice: Voice = {
      ...props.voice,
      ...formData.value,
      tags: Array.isArray(formData.value.tags) ? [...formData.value.tags] : props.voice.tags,
      referencePath: Array.isArray(formData.value.referencePath) ? [...formData.value.referencePath] : props.voice.referencePath
    };

    const updatedVoice = await window.electronAPI.updateVoice(updatingVoice);
    emit('update', updatedVoice);
    emit('update:show', false);
    showToast('success', t('voices.update.success'));
  } catch (error) {
    console.error('Lỗi khi cập nhật voice:', error);
    showToast('error', t('voices.update.error.failed'));
  } finally {
    isLoading.value = false;
  }
};

// Hàm xử lý đóng dialog
const handleClose = () => {
  emit('update:show', false);
};
</script>

<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center pt-16">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-[600px] max-h-[90vh] overflow-y-auto">
      <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-white">
        {{ t('voices.update.title') }}
      </h3>

      <div class="space-y-4">
        <!-- Tên voice -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('voices.update.nameLabel') }}
          </label>
          <input 
            v-model="formData.name"
            type="text"
            :readonly="voice.type === 'builtin'"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            :class="{ 'bg-gray-100 dark:bg-gray-700 cursor-not-allowed': voice.type === 'builtin' }"
            :placeholder="t('voices.update.namePlaceholder')"
          />
        </div>

        <!-- Mô tả -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('voices.update.descriptionLabel') }}
          </label>
          <textarea 
            v-model="formData.description"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            :placeholder="t('voices.update.descriptionPlaceholder')"
          />
        </div>

        <!-- Ngôn ngữ -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('voices.update.languageLabel') }}
          </label>
          <select 
            v-model="formData.language"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="en">English</option>
            <option value="vi">Tiếng Việt</option>
          </select>
        </div>

        <!-- Tags -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('voices.update.tagsLabel') }}
          </label>
          <input 
            v-model="formData.tags"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            :placeholder="t('voices.update.tagsPlaceholder')"
          />
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {{ t('voices.update.tagsHint') }}
          </p>
        </div>

        <!-- Reference Files -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('voices.update.referenceLabel') }}
          </label>
          <div class="space-y-2">
            <div v-for="(ref, index) in formData.referencePath" :key="index" class="flex items-center space-x-2">
              <span class="flex-1 text-sm text-gray-600 dark:text-gray-400 truncate">
                {{ ref.split('/').pop() }}
              </span>
              <button 
                @click="handleRemoveReference(index)"
                class="text-red-500 hover:text-red-600"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
              </button>
            </div>
            <button 
              v-if="!formData.referencePath || formData.referencePath.length < 5"
              @click="handleAddReference"
              class="w-full px-4 py-2 border border-dashed border-gray-300 dark:border-gray-600 rounded-md text-gray-600 dark:text-gray-400 hover:border-blue-500 hover:text-blue-500 dark:hover:border-blue-400 dark:hover:text-blue-400"
            >
              {{ t('voices.update.reference.add') }}
            </button>
          </div>
        </div>

        <!-- File paths for finetuned/imported voices -->
        <template v-if="voice.type === 'finetuned' || voice.type === 'imported'">
          <!-- Model Path -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voices.update.modelPathLabel') }}
            </label>
            <div class="flex space-x-2">
              <input 
                v-model="formData.modelPath"
                type="text"
                readonly
                class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
              <button 
                @click="handleSelectFile('model')"
                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                {{ t('voices.update.buttons.select') }}
              </button>
            </div>
          </div>

          <!-- Config Path -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voices.update.configPathLabel') }}
            </label>
            <div class="flex space-x-2">
              <input 
                v-model="formData.configPath"
                type="text"
                readonly
                class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
              <button 
                @click="handleSelectFile('config')"
                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                {{ t('voices.update.buttons.select') }}
              </button>
            </div>
          </div>

          <!-- Vocab Path -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voices.update.vocabPathLabel') }}
            </label>
            <div class="flex space-x-2">
              <input 
                v-model="formData.vocabPath"
                type="text"
                readonly
                class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
              <button 
                @click="handleSelectFile('vocab')"
                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                {{ t('voices.update.buttons.select') }}
              </button>
            </div>
          </div>
        </template>

        <!-- Preview Path -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('voices.update.previewPathLabel') }}
          </label>
          <div class="flex space-x-2">
            <input 
              v-model="formData.previewPath"
              type="text"
              readonly
              class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
            <button 
              @click="handleSelectFile('preview')"
              class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              {{ t('voices.update.buttons.select') }}
            </button>
          </div>
        </div>

        <!-- Các thông số khác -->
        <div class="grid grid-cols-2 gap-4">
          <!-- Speed -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voices.update.speedLabel') }}
            </label>
            <input 
              v-model.number="formData.speed"
              type="number"
              step="0.1"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <!-- Temperature -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voices.update.temperatureLabel') }}
            </label>
            <input 
              v-model.number="formData.temperature"
              type="number"
              step="0.1"
              min="0"
              max="1"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <!-- Repetition Penalty -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voices.update.repetitionPenaltyLabel') }}
            </label>
            <input 
              v-model.number="formData.repetitionPenalty"
              type="number"
              step="0.1"
              min="0"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <!-- Top K -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voices.update.topKLabel') }}
            </label>
            <input 
              v-model.number="formData.topK"
              type="number"
              min="1"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <!-- Top P -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voices.update.topPLabel') }}
            </label>
            <input 
              v-model.number="formData.topP"
              type="number"
              step="0.1"
              min="0"
              max="1"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <!-- Length Penalty -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voices.update.lengthPenaltyLabel') }}
            </label>
            <input 
              v-model.number="formData.lengthPenalty"
              type="number"
              step="0.1"
              min="0"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
      </div>

      <!-- Buttons -->
      <div class="flex justify-between space-x-2 mt-6">
        <div class="flex space-x-2">
          <button 
            v-if="voice.type === 'finetuned' || voice.type === 'imported'"
            @click="handleDelete"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            :disabled="isLoading"
          >
            <span v-if="isLoading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ t('common.loading') }}
            </span>
            <span v-else>{{ t('voices.manage.actions.delete') }}</span>
          </button>
        </div>
        <div class="flex space-x-2">
          <button 
            @click="handleClose"
            class="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
          >
            {{ t('common.cancel') }}
          </button>
          <button 
            @click="handleUpdate"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            :disabled="isLoading"
          >
            <span v-if="isLoading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ t('common.loading') }}
            </span>
            <span v-else>{{ t('voices.update.buttons.update') }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Confirm Modal -->
  <ConfirmModal
    v-model:show="showDeleteConfirm"
    :title="t('voices.manage.actions.deleteConfirmTitle')"
    :message="t('voices.manage.actions.deleteConfirmMessage', { name: voice.name })"
    :show-cancel="true"
    @close="showDeleteConfirm = false"
    @confirm="confirmDelete"
  />
</template> 