{"app": {"title": "Gen Voice", "theme": {"light": "<PERSON><PERSON><PERSON>", "dark": "<PERSON><PERSON><PERSON>"}}, "common": {"loading": "<PERSON><PERSON> tả<PERSON>...", "error": "Đ<PERSON> xảy ra lỗi", "success": "<PERSON><PERSON><PERSON><PERSON> công", "buttons": {"confirm": "<PERSON><PERSON><PERSON>", "cancel": "Hủy bỏ"}, "import": "<PERSON><PERSON><PERSON><PERSON>", "importing": "<PERSON><PERSON> nhập...", "cancel": "<PERSON><PERSON><PERSON>"}, "home": {"title": "Text To Speech", "form": {"text": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> văn bản cần chuyển đổi...", "characterCount": {"empty": "<PERSON><PERSON><PERSON> có nội dung", "count": "{count} ký tự", "minimum": "<PERSON><PERSON><PERSON> thiểu {minimum} ký tự"}}, "voice": {"label": "<PERSON><PERSON><PERSON> voice", "placeholder": "<PERSON><PERSON><PERSON> voice"}, "language": {"label": "<PERSON><PERSON><PERSON>"}, "speed": {"label": "<PERSON><PERSON><PERSON> độ: {speed}", "tooltipTitle": "<PERSON><PERSON><PERSON> độ phát <PERSON>h", "tooltip": "Điều chỉnh tốc độ phát của giọng nói. Gi<PERSON> trị lớn hơn 1 sẽ <PERSON><PERSON><PERSON> hơn, nhỏ hơn 1 sẽ chậm hơn.", "exampleSlow": "<PERSON><PERSON><PERSON>", "exampleNormal": "<PERSON><PERSON><PERSON>", "exampleFast": "<PERSON><PERSON><PERSON>", "exampleVeryFast": "<PERSON><PERSON><PERSON>"}, "temperature": {"label": "Temperature: {value}", "tooltipTitle": "Nhiệt độ mẫu", "tooltip": "Điều chỉnh mức độ ngẫu nhiên trong quá trình sinh âm thanh. <PERSON><PERSON><PERSON> trị thấp (0.3-0.5) sẽ tạo ra âm thanh ổn định và nhất quán h<PERSON>, trong khi giá trị cao sẽ tạo ra nhiều biến thể sáng tạo nhưng có thể mất đặc trưng giọng gốc.", "exampleLow": "0.3-0.5: <PERSON><PERSON> <PERSON><PERSON>, nh<PERSON><PERSON> quán", "exampleHigh": "0.7-1.0: <PERSON><PERSON><PERSON><PERSON> biến thể sáng tạo"}, "repetitionPenalty": {"label": "Repetition Penalty: {value}", "tooltipTitle": "<PERSON>ệ số phạt lặp lại", "tooltip": "Điều chỉnh mức độ phạt khi có sự lặp lại âm tiết hoặc từ. <PERSON><PERSON><PERSON> trị cao hơn (3.0-5.0) sẽ giúp tránh hiện tượng lặp lại không mong muốn, nh<PERSON><PERSON> không nên đặt quá cao để tránh làm mất đặc điểm nhịp điệu tự nhiên của giọng.", "exampleLow": "1.0-2.0: <PERSON><PERSON> phạt lặp lại", "exampleHigh": "3.0-5.0: Phạt lặp lại mạnh"}, "topK": {"label": "Top K: {value}", "tooltipTitle": "Số lượng token có xác suất cao nhất", "tooltip": "Giới hạn số lượng lựa chọn tiềm năng khi sinh âm thanh. <PERSON><PERSON><PERSON> trị thấp hơn (20-30) sẽ giúp mô hình tập trung vào các mẫu có xác suất cao và gần với giọng gốc hơn.", "exampleLow": "20-30: Tậ<PERSON> trung vào mẫu có xác suất cao", "exampleHigh": "40-50: <PERSON><PERSON> x<PERSON>t nhi<PERSON>u l<PERSON>a chọn h<PERSON>n"}, "topP": {"label": "Top P: {value}", "tooltipTitle": "Ngưỡng x<PERSON>c su<PERSON>t tích l<PERSON>y", "tooltip": "Giới hạn tổng xác suất tích lũy khi chọn token. G<PERSON><PERSON> trị thấp hơn (0.6-0.7) sẽ giúp mô hình chỉ xem xét các lựa chọn có xác suất cao, tạo ra âm thanh gần với giọng gốc hơn.", "exampleLow": "0.6-0.7: <PERSON><PERSON><PERSON> trung vào x<PERSON>c su<PERSON>t cao", "exampleHigh": "0.8-1.0: <PERSON><PERSON> x<PERSON>t <PERSON>u x<PERSON><PERSON> h<PERSON>n"}, "lengthPenalty": {"label": "Length Penalty: {value}", "tooltipTitle": "<PERSON><PERSON> số điều chỉnh độ dài", "tooltip": "<PERSON>iều chỉnh độ dài của âm thanh được tạo ra. <PERSON>i<PERSON> trị thấp hơn sẽ tạo ra âm thanh ngắn hơn, trong khi giá trị cao hơn sẽ tạo ra âm thanh dài hơn.", "exampleLow": "0.0-0.5: <PERSON><PERSON> <PERSON><PERSON> ng<PERSON> h<PERSON>n", "exampleHigh": "1.5-2.0: <PERSON><PERSON> <PERSON><PERSON> dà<PERSON> h<PERSON>n"}, "numThreads": {"label": "<PERSON><PERSON> luồng xử lý: {value}", "tooltipTitle": "<PERSON><PERSON> luồng xử lý", "tooltip": "Số luồng CPU được sử dụng để xử lý. Tăng số luồng có thể tăng tốc độ xử lý trên máy đa nhân. <PERSON><PERSON>, đặt giá trị quá cao có thể gây quá tải hệ thống.", "example2": "2 luồng: <PERSON><PERSON> hợp với hầu hết máy t<PERSON> (bao gồm MacBook Air)", "example4": "4 luồng: Chỉ dùng cho máy c<PERSON> <PERSON> mạnh (8+ nhân)", "warning": "L<PERSON>u ý: <PERSON><PERSON><PERSON><PERSON> nên đặt cao hơn số nhân CPU thực tế"}, "numWorkers": {"label": "Số worker: {value}", "tooltipTitle": "Số worker <PERSON><PERSON> lý", "tooltip": "Số worker x<PERSON> lý song song các tác vụ TTS. Mỗi worker tiêu tốn nhiều RAM. Cần cân nhắc RAM khả dụng khi điều chỉnh giá trị này.", "example2": "2 worker: <PERSON><PERSON> với 8GB RAM (MacBook Air)", "example4": "4 worker: Chỉ dùng cho máy có 16GB+ RAM", "warning": "Lưu ý: Mỗi worker có thể chiếm 2-4GB RAM, đ<PERSON><PERSON> bảo <PERSON> khả dụng đủ cho số worker đã chọn"}, "outputFolder": {"label": "<PERSON><PERSON><PERSON> m<PERSON> file", "placeholder": "<PERSON><PERSON><PERSON> thư mục lưu file...", "select": "<PERSON><PERSON><PERSON>"}, "referenceFiles": {"label": "File âm thanh tham chiếu", "placeholder": "<PERSON><PERSON><PERSON> file âm thanh tham chiếu...", "select": "<PERSON><PERSON><PERSON>"}, "buttons": {"preview": "<PERSON><PERSON> thử", "stop": "Dừng", "convert": "<PERSON><PERSON><PERSON><PERSON> đổi", "processing": "<PERSON><PERSON> xử lý..."}, "fileName": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên file (t<PERSON><PERSON> chọn)"}, "advancedOptions": {"label": "<PERSON><PERSON><PERSON> ch<PERSON>n nâng cao", "tooltip": "<PERSON><PERSON><PERSON> thị/<PERSON>n tùy chọn nâng cao"}}, "process": {"title": "<PERSON><PERSON><PERSON><PERSON> lý quá trình", "empty": "Chưa có quá trình nào", "columns": {"fileName": "Tên file", "text": "<PERSON><PERSON>i dung", "voice": "<PERSON><PERSON><PERSON><PERSON> nói", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "executionTime": "<PERSON>h<PERSON>i gian thực thi", "duration": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "actions": "<PERSON><PERSON>"}, "status": {"pending": "<PERSON><PERSON> chờ", "processing": "<PERSON><PERSON> lý", "running": "<PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "cancelled": "<PERSON><PERSON> hủy", "error": "Lỗi", "saving": "<PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON> th<PERSON>"}, "actions": {"cleanup": "Dọn dẹp", "cancel": "<PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON> lại", "delete": "Xóa"}, "chunks": {"title": "<PERSON> tiết các đo<PERSON>n ({count})", "empty": "Chờ xử lý...", "chunk": "<PERSON><PERSON><PERSON><PERSON> {index}", "edit": {"button": "Chỉnh sửa đoạn", "retry": "S<PERSON>a và thử lại", "placeholder": "Nhập text mới cho đoạn này...", "save": "Lưu & <PERSON><PERSON>ử lại", "cancel": "<PERSON><PERSON><PERSON>", "reloading": "<PERSON><PERSON> chạy lại..."}, "info": {"duration": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "audio": "Audio"}}}}, "voices": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tabs": {"manage": "<PERSON><PERSON><PERSON><PERSON>", "finetune": "<PERSON><PERSON><PERSON> l<PERSON> Voice"}, "manage": {"empty": "<PERSON><PERSON><PERSON> c<PERSON> voice nào", "actions": {"play": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "export": "<PERSON><PERSON><PERSON>", "import": "<PERSON><PERSON><PERSON><PERSON>", "noPreview": "Không có file preview cho voice này", "playError": "<PERSON>h<PERSON>ng thể phát file âm thanh", "deleteSuccess": "Đ<PERSON> xóa voice thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa voice", "deleteConfirmTitle": "Xóa Voice", "deleteConfirmMessage": "<PERSON>ạn có chắc chắn muốn xóa voice \"{name}\"? Hành động này không thể hoàn tác."}, "export": {"success": "<PERSON><PERSON><PERSON> voice thành công", "error": {"missingFiles": "Voice thiếu các file cần thiết", "failed": "<PERSON><PERSON><PERSON><PERSON> thể xuất voice"}}, "import": {"title": "Nhập Voice", "nameLabel": "<PERSON><PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên voice (<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> buộc)", "success": "<PERSON><PERSON><PERSON><PERSON> voice thành công", "error": {"failed": "<PERSON><PERSON><PERSON><PERSON> thể nhập voice"}}}, "finetune": {"title": "<PERSON><PERSON><PERSON> luy<PERSON>n Voice mới", "form": {"voiceName": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên cho voice mới"}, "inputFolder": {"label": "<PERSON><PERSON><PERSON> m<PERSON>c dữ liệu", "placeholder": "Đường dẫn thư mục chứa file wav và txt", "select": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> mụ<PERSON> cần chứa các cặp file audio (.wav, .mp3, .flac) và .txt có cùng tên (ví dụ: audio001.wav và audio001.txt)"}, "duration": {"good": "Tốt: 3-30 giây", "acceptable": "<PERSON><PERSON><PERSON> <PERSON>h<PERSON>n đượ<PERSON>: <3s hoặc 30-60 giây", "notRecommended": "<PERSON><PERSON><PERSON>ng khuyến nghị: >60 giây"}}, "metadata": {"title": "<PERSON><PERSON> lý tài nguyên huấn luyện", "processing": "<PERSON>ang xử lý metadata...", "processed": "Metadata đã xử lý", "stats": {"totalFiles": "Tổng số file:", "totalDuration": "<PERSON><PERSON><PERSON> thời lư<PERSON>:", "trainCount": "<PERSON><PERSON> liệu huấn luyện:", "evalCount": "<PERSON><PERSON> liệu đ<PERSON>h giá:"}, "table": {"file": "File", "duration": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "text": "<PERSON><PERSON><PERSON>"}, "buttons": {"process": "<PERSON><PERSON> lý tài nguyên huấn luyện", "startTraining": "<PERSON><PERSON><PERSON> đ<PERSON>u hu<PERSON>n luy<PERSON>n", "cancel": "<PERSON><PERSON><PERSON><PERSON> lý"}}, "training": {"title": "<PERSON><PERSON><PERSON><PERSON> trình huấn luyện", "empty": "<PERSON><PERSON><PERSON> có tiến trình huấn luyện nào đang chạy", "status": {"processing": "<PERSON><PERSON> lý", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "progress": "<PERSON><PERSON><PERSON><PERSON> độ: {progress}%", "buttons": {"cancel": "<PERSON><PERSON><PERSON> hu<PERSON> l<PERSON>"}}, "confirmCancel": {"title": "<PERSON><PERSON><PERSON>n hủy huấn luyện", "message": "Bạn có chắc chắn muốn hủy quá trình huấn luyện này không? Tiến trình đã hoàn thành sẽ bị mất và không thể khôi phục.", "buttons": {"cancel": "Hủy bỏ", "confirm": "<PERSON><PERSON><PERSON> h<PERSON>"}}}, "update": {"title": "<PERSON><PERSON><PERSON>", "nameLabel": "<PERSON><PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên voice", "descriptionLabel": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả voice", "languageLabel": "<PERSON><PERSON><PERSON>", "tagsLabel": "Tags", "tagsPlaceholder": "Nhập tags (phân cách bằng dấu phẩy)", "tagsHint": "<PERSON><PERSON> cách tags bằng dấu phẩy", "referenceLabel": "File tham chi<PERSON>u", "reference": {"add": "Thêm file tham chiếu", "maxLimit": "<PERSON><PERSON>i đa 5 file tham chiếu", "error": {"add": "<PERSON><PERSON><PERSON><PERSON> thể thêm file tham chiếu"}}, "speedLabel": "<PERSON><PERSON><PERSON>", "temperatureLabel": "Nhiệt độ", "repetitionPenaltyLabel": "<PERSON><PERSON> số lặp lại", "topKLabel": "Top K", "topPLabel": "Top P", "lengthPenaltyLabel": "<PERSON><PERSON> số độ dài", "modelPathLabel": "Đường dẫn Model", "configPathLabel": "Đường dẫn Config", "vocabPathLabel": "Đường dẫn Vocab", "previewPathLabel": "Đường dẫn Preview", "buttons": {"update": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>"}, "error": {"nameRequired": "Tên voice là b<PERSON><PERSON> bu<PERSON>c", "selectFile": "<PERSON><PERSON><PERSON><PERSON> thể chọn file", "failed": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật voice", "invalidFileType": "Định dạng file kh<PERSON><PERSON> hợp lệ. <PERSON><PERSON> lòng chọn đúng định dạng file"}, "success": "<PERSON><PERSON><PERSON> nh<PERSON>t voice thành công"}}, "cleanup": {"title": "Dọn dẹp tiến trình", "message": "Điều này sẽ xóa tất cả các tiến trình đã hoàn thành, thất bại và đã hủy. Bạn có chắc chắn?", "cleanupSuccess": "<PERSON><PERSON> dọn dẹp tiến trình", "cleanupError": "<PERSON><PERSON><PERSON><PERSON> thể dọn dẹp tiến trình"}, "settings": {"title": "Cài đặt", "userDataFolder": {"label": "<PERSON><PERSON><PERSON> mục dữ liệu ứng dụng", "openButton": "Mở thư mục", "description": "Mở thư mục chứa dữ liệu của ứng dụng (voices, logs, cài đặt)", "openSuccess": "<PERSON><PERSON> mở thư mục dữ liệu", "openError": "<PERSON><PERSON><PERSON><PERSON> thể mở thư mục dữ liệu"}}}