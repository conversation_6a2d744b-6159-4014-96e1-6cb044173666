import {v4 as uuidv4} from 'uuid';
import {runPythonScript} from './PythonService';
import type {Voice, VoiceType} from '@types';
import {getVoiceStore} from '@main/store/VoiceStore';
import {WindowManager} from './WindowManager';
import path from 'path';
import fs from 'fs';
import {getStoreValue} from '@main/utils/store-helper';

export class FinetuneService {
  private static instance: FinetuneService;
  private voiceStore: ReturnType<typeof getVoiceStore>;
  private windowManager: WindowManager;

  private constructor() {
    this.voiceStore = getVoiceStore();
    this.windowManager = WindowManager.getInstance();
  }

  public static getInstance(): FinetuneService {
    if (!FinetuneService.instance) {
      FinetuneService.instance = new FinetuneService();
    }
    return FinetuneService.instance;
  }

  public async processMetadata(params: {
    processId: string;
    inputFolder: string;
    voiceName: string;
    language?: string;
    voicesPath: string;
  }): Promise<{ processId: string; processorResult: any } | { error: string }> {
    try {
      const outputFolder = path.join(params.voicesPath, params.voiceName);

      if (!fs.existsSync(outputFolder)) {
        fs.mkdirSync(outputFolder, {recursive: true});
      }

      const args = [
        '--input', params.inputFolder,
        '--output', outputFolder,
        '--language', params.language || 'en',
        '--speaker_name', params.voiceName,
        '--process_id', params.processId
      ];

      const processorResult = await runPythonScript(
        'metadata_processor',
        args,
        {
          onProgress: (data) => {
            this.windowManager.sendToRenderer('metadataProgress', data);
          },
          onMessage: (message) => {
            try {
              const parsedMessage = JSON.parse(message);
              this.windowManager.sendToRenderer('metadata-message', {
                message: parsedMessage
              });
            } catch (error) {
              console.error('Error parsing metadata message:', error);
              this.windowManager.sendToRenderer('metadata-error', {
                error: 'Invalid message format'
              });
            }
          },
          onComplete: (result) => {
            this.windowManager.sendToRenderer('metadataComplete', result);
          },
          onError: (error) => {
            this.windowManager.sendToRenderer('metadata-error', {
              error
            });
          }
        }
      );

      return {processId: params.processId, processorResult};
    } catch (error) {
      console.error('Error processing metadata:', error);
      return {error: (error as Error).message};
    }
  }

  public async startFinetune(params: {
    voiceName: string;
    outputFolder: string;
    trainMetadataPath?: string;
    evalMetadataPath?: string;
    epochs?: number;
    testText?: string;
  }): Promise<{ processId: string } | { error: string }> {
    try {
      // Tạo ID cho tiến trình
      const processId = uuidv4();

      // Tạo danh sách tham số
      const args = [
        '--train_metadata', params.trainMetadataPath || '',
        '--eval_metadata', params.evalMetadataPath || '',
        '--output', params.outputFolder,
        '--name', params.voiceName,
        '--epochs', (params.epochs || 10).toString(),
        '--batch_size', '10',
      ];

      // Thêm tham số tùy chọn
      if (params.testText) {
        args.push('--test_text', params.testText);
      }

      // Chạy script Python
      const result = await runPythonScript(
        'finetune',
        args,
        {
          onProgress: (progress) => {
            this.windowManager.sendToRenderer('finetuneProgress', {processId, progress});
          },
          onLog: (log) => {
            this.windowManager.sendToRenderer('finetuneLog', {processId, log});
          },
          onComplete: (result) => {
            this.windowManager.sendToRenderer('finetuneComplete', {processId, result});

            // Thêm các voices mới vào voice-store
            if (result && result.checkpoints) {
              for (const checkpoint of result.checkpoints) {
                const voice: Voice = {
                  id: uuidv4(),
                  name: `${params.voiceName}_${checkpoint.epoch}`,
                  createdAt: new Date().toISOString(),
                  language: 'en', // Mặc định là tiếng Anh
                  modelPath: checkpoint.model_path,
                  configPath: checkpoint.config_path,
                  vocabPath: checkpoint.vocab_path,
                  previewPath: checkpoint.preview_path,
                  referencePath: [],
                  temperature: 0.4,
                  repetitionPenalty: 4,
                  topK: 25,
                  topP: 0.65,
                  lengthPenalty: 1,
                  speed: 1.0,
                  type: 'finetuned' as VoiceType
                };

                this.voiceStore.addVoice(voice);
              }
            }
          },
          onError: (error) => {
            this.windowManager.sendToRenderer('finetuneError', {processId, error});
          }
        }
      );

      const pythonProcessId = result.toString();
      return {processId: pythonProcessId};
    } catch (error) {
      console.error('Error starting finetune:', error);
      return {error: String(error)};
    }
  }
}