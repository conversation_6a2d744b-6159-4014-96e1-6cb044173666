import type {TTSQueueItem} from '@types';
import logger from '../logger/client';
import {WindowManager} from './WindowManager';
import {TTSProcessStore} from '../store/TTSProcessStore';
import os from 'os';
import {killPythonProcess} from './PythonService';
import {SpeechService} from './SpeechService';

export class TTSQueue {
  private static instance: TTSQueue;
  private queue: TTSQueueItem[] = [];
  private isProcessing = false;
  private readonly maxConcurrentTasks: number;
  private activeTasks: Map<string, TTSQueueItem> = new Map();
  private log = logger.channel('tts-queue');
  private windowManager: WindowManager;
  private processStore: TTSProcessStore;
  private isPaused = false;
  private speechService: SpeechService;

  private constructor(maxConcurrentTasks: number = 1) {
    this.maxConcurrentTasks = maxConcurrentTasks;
    this.windowManager = WindowManager.getInstance();
    this.processStore = TTSProcessStore.getInstance();
    this.speechService = SpeechService.getInstance();
  }

  public static getInstance(): TTSQueue {
    if (!TTSQueue.instance) {
      TTSQueue.instance = new TTSQueue();
    }
    return TTSQueue.instance;
  }

  public async addTask(task: TTSQueueItem): Promise<void> {

    // Kiểm tra xem processId đã tồn tại trong queue hoặc đang xử lý chưa
    if (this.queue.some(item => item.processId === task.processId) ||
        this.activeTasks.has(task.processId)) {
      throw new Error(`Task with processId ${task.processId} already exists`);
    }

    this.queue.push(task);
    this.log.info('TTS-QUEUE : Added new task to queue', {processId: task.processId});

    // Cập nhật trạng thái trong store
    const process = this.processStore.getProcess(task.processId);
    if (process) {
      process.status = 'pending';
      process.progress = 0;
      process.updatedAt = new Date().toISOString();
      this.processStore.updateProcess(process);
    }

    // Gửi trạng thái processing khi task được thêm vào queue
    this.windowManager.sendToRenderer('onUpdateProcess', {
      processId: task.processId,
      status: 'pending',
      progress: 0,
      updatedAt: new Date().toISOString()
    });

    // Bắt đầu xử lý queue nếu chưa có task nào đang chạy
    if (!this.isProcessing) {
      await this.processNextTask();
    }
  }

  public async checkMemoryAndPauseIfNeeded() {
    const freeMemory = os.freemem();
    const freeMemoryGB = freeMemory / (1024 * 1024 * 1024);
    
    this.log.info('Checking available memory', { freeMemoryGB });
    
    if (freeMemoryGB < 5) {
      this.log.warning('Low memory detected, pausing queue', { freeMemoryGB });
      await this.pauseQueue();
      return;
    }
  }

  public async pauseQueue(): Promise<void> {
    if (this.isPaused) return;

    this.isPaused = true;
    this.log.info('Pausing TTS queue');

    // Kill any running processes
    for (const [processId, _] of this.activeTasks) {
      try {
        this.log.info('Killing running process', { processId });
        killPythonProcess(processId);

      } catch (error) {
        this.log.error('Error killing process', { processId, error });
      }
    }
    this.activeTasks.clear();

    // Update all processes to paused state
    const processes = this.processStore.getProcesses();
    if (processes) {
      for (const process of processes) {
        if (process.status === 'running' || process.status === 'processing') {
          this.log.info('Updating process status to paused', { processId: process.id });
          this.processStore.updateProcess({
            ...process,
            status: 'paused'
          });
        }
      }
    }
  }

  public async resumeQueue(): Promise<void> {
    if (!this.isPaused) return;

    this.isPaused = false;
    this.log.info('Resuming TTS queue');

    // Update status of all paused processes back to pending
    const processes = this.processStore.getProcesses();
    for (const process of processes) {
      if (process.status === 'paused') {
        process.status = 'pending';
        process.updatedAt = new Date().toISOString();
        this.processStore.updateProcess(process);

        // Notify renderer
        this.windowManager.sendToRenderer('onUpdateProcess', {
          processId: process.id,
          status: 'pending',
          updatedAt: process.updatedAt
        });
      }
    }

    // Start processing queue again
    if (!this.isProcessing) {
      this.processNextTask();
    }
  }

  public async reorderQueue(newOrder: string[]): Promise<void> {
    // Get all processes
    const processes = this.processStore.getProcesses();
    
    // Update process order in store first
    const orderedProcesses = newOrder.map(id => processes.find(p => p.id === id)).filter(p => p !== undefined);
    
    // Add any processes not in the new order
    orderedProcesses.push(...processes.filter(p => !newOrder.includes(p.id)));
    
    // Update store with new order
    this.processStore.setProcesses(orderedProcesses);

    // Notify renderer about the new order
    this.windowManager.sendToRenderer('onUpdateProcess', {
      processId: 'reorder',
      status: 'reordered',
      processes: orderedProcesses
    });

    // Only update queue for processes with specific statuses
    const queueableStatuses = ['pending', 'paused', 'running', 'processing'];
    const queueableProcesses = orderedProcesses.filter(p => queueableStatuses.includes(p.status));

    // Create a map of processId to queue item for quick lookup
    const queueMap = new Map(this.queue.map(item => [item.processId, item]));
    
    // Create new queue based on new order, only including queueable processes
    const newQueue: TTSQueueItem[] = [];
    for (const process of queueableProcesses) {
      const item = queueMap.get(process.id);
      if (item) {
        newQueue.push(item);
        queueMap.delete(process.id);
      }
    }
    
    // Add any remaining items that weren't in the new order
    newQueue.push(...queueMap.values());
    
    // Update queue
    this.queue = newQueue;
  }

  private async processNextTask(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0 || this.isPaused) return;

    this.isProcessing = true;
    try {
      while (this.queue.length > 0 && this.activeTasks.size < this.maxConcurrentTasks && !this.isPaused) {
        const task = this.queue.shift();
        if (!task) continue;

        const processId = task.processId;
        this.activeTasks.set(processId, task);

        logger.info('TTS-QUEUE : Processing TTS task', {processId});

        try {
          await task.execute();
        } catch (error) {
          logger.error('TTS-QUEUE : Error processing task', {processId, error});
          const process = this.processStore.getProcess(processId);
          if (process) {
            let executionTime = process.executionTime || 0;
            
            if (process.reloadTime) {
              // Nếu có reloadTime, cộng thêm thời gian từ reloadTime đến hiện tại
              executionTime += new Date().getTime() - new Date(process.reloadTime).getTime();
            } else if (process.startTime) {
              // Nếu không có reloadTime, tính từ startTime
              executionTime = new Date().getTime() - new Date(process.startTime).getTime();
            }

            process.status = 'failed';
            process.error = String(error);
            process.executionTime = executionTime;
            process.updatedAt = new Date().toISOString();
            this.processStore.updateProcess(process);

            this.windowManager.sendToRenderer('onUpdateProcess', {
              processId,
              status: 'failed',
              error: String(error),
              executionTime,
              updatedAt: new Date().toISOString()
            });
          }
          if (task.onError) {
            task.onError(error);
          }
        } finally {
          this.activeTasks.delete(processId);
        }
      }
    } finally {
      this.isProcessing = false;
      if (this.queue.length > 0 && !this.isPaused) {
        this.processNextTask();
      }
    }
  }

  async cancelTask(processId: string): Promise<boolean> {
    // Kiểm tra trong queue
    const queueIndex = this.queue.findIndex(item => item.processId === processId);
    if (queueIndex !== -1) {
      this.queue.splice(queueIndex, 1);
      logger.info('TTS-QUEUE : Cancelled queued task', {processId});

      // Cập nhật trạng thái trong store
      const process = this.processStore.getProcess(processId);
      if (process) {
        let executionTime = process.executionTime || 0;
        
        if (process.reloadTime) {
          // Nếu có reloadTime, cộng thêm thời gian từ reloadTime đến hiện tại
          executionTime += new Date().getTime() - new Date(process.reloadTime).getTime();
        } else if (process.startTime) {
          // Nếu không có reloadTime, tính từ startTime
          executionTime = new Date().getTime() - new Date(process.startTime).getTime();
        }

        process.status = 'cancelled';
        process.executionTime = executionTime;
        process.updatedAt = new Date().toISOString();
        this.processStore.updateProcess(process);

        // Gửi thông báo về renderer
        this.windowManager.sendToRenderer('onUpdateProcess', {
          processId,
          status: 'cancelled',
          executionTime,
          updatedAt: new Date().toISOString()
        });
      }
      return true;
    }

    // Kiểm tra trong active tasks
    const activeTask = this.activeTasks.get(processId);
    if (activeTask) {
      // Gọi hàm cancel của task nếu có
      if (activeTask.cancel) {
        await activeTask.cancel();
      }
      this.activeTasks.delete(processId);
      logger.info('TTS-QUEUE : Cancelled active task', {processId});

      // Cập nhật trạng thái trong store
      const process = this.processStore.getProcess(processId);
      if (process) {
        let executionTime = process.executionTime || 0;
        
        if (process.reloadTime) {
          // Nếu có reloadTime, cộng thêm thời gian từ reloadTime đến hiện tại
          executionTime += new Date().getTime() - new Date(process.reloadTime).getTime();
        } else if (process.startTime) {
          // Nếu không có reloadTime, tính từ startTime
          executionTime = new Date().getTime() - new Date(process.startTime).getTime();
        }

        process.status = 'cancelled';
        process.executionTime = executionTime;
        process.updatedAt = new Date().toISOString();
        this.processStore.updateProcess(process);

        // Gửi thông báo về renderer
        this.windowManager.sendToRenderer('onUpdateProcess', {
          processId,
          status: 'cancelled',
          executionTime,
          updatedAt: new Date().toISOString()
        });
      }
      return true;
    }

    return false;
  }

  async deleteTask(processId: string): Promise<boolean> {
    // Hủy task nếu đang chạy
    const cancelled = await this.cancelTask(processId);

    // Xóa khỏi store (async)
    await this.processStore.deleteProcess(processId);

    return cancelled;
  }

  getQueueStatus(): { queued: number; active: number } {
    return {
      queued: this.queue.length,
      active: this.activeTasks.size
    };
  }

  /**
   * Khôi phục queue từ store khi app khởi động hoặc reload
   * - Thêm các process có trạng thái pending, processing, running vào queue theo thứ tự
   * - Nếu process có chunk đã completed thì chỉ chạy tiếp các chunk chưa completed
   */
  public async restoreQueueFromStore(): Promise<void> {
    try {
      // Lấy danh sách process từ store
      const processes = this.processStore.getProcesses();
      
      // Lọc ra các process cần khôi phục (pending, running, processing)
      const processesToRestore = processes.filter(p => 
        ['pending', 'running', 'processing', 'failed'].includes(p.status)
      );

      this.log.info('Restoring queue from store', {
        totalProcesses: processes.length,
        processesToRestore: processesToRestore.length
      });

      // Thêm các process vào queue
      for (const process of processesToRestore) {
        // Tạo task mới
        const task: TTSQueueItem = {
          processId: process.id,
          execute: async () => {
            await this.speechService.textToSpeech({
              text: process.text,
              voice: process.voice,
              outputPath: process.outputPath,
              language: 'en',
              processId: process.id,
              fileName: process.fileName,
              numThreads: process.numThreads,
              numWorkers: process.numWorkers
            });
          }
        };

        // Thêm task vào queue
        await this.addTask(task);
      }

      this.log.info('Queue restored successfully', {
        restoredProcesses: processesToRestore.length
      });
    } catch (error) {
      this.log.error('Error restoring queue', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }
}