import {app, BrowserWindow, dialog, ipcMain, protocol, shell} from 'electron';
import path from 'path';
import fs from 'fs';
import {execSync} from 'child_process';
import {getPythonProcessInfo, killPythonProcess} from './service/PythonService';
import type {LogChannel, LogLevel} from './logger/core';
import loggerCore from './logger/core';
import logger from './logger/client';
import {getVoiceStore} from '@main/store/VoiceStore';
import {WindowManager} from '@main/service/WindowManager';
import {FinetuneService} from '@main/service/FinetuneService';
import {SpeechService} from '@main/service/SpeechService';
import type {TextToSpeechParams, TTSProcess, TTSQueueItem, Voice} from '@types';
import {TTSQueue} from '@main/service/TTSQueue';
import {TTSProcessStore} from '@main/store/TTSProcessStore';
import {createStore, getStoreValue, setStoreValue} from '@main/utils/store-helper';
import {ReloadChunk} from "../types";

// Khởi tạo electron-store để lưu cài đặt
const store = createStore({
  defaults: {
    theme: 'dark',
    language: 'en',
    speed: 1.0,
    defaultVoice: '',
    outputPath: app.getPath('downloads'),
    voicesPath: path.join(app.getPath('userData'), 'voices'),
    tempDir: path.join(app.getPath('userData'), 'temp')
  }
});

// Đảm bảo thư mục voices tồn tại
const voicesPath = getStoreValue(store, 'voicesPath') as string;
if (!fs.existsSync(voicesPath)) {
  fs.mkdirSync(voicesPath, {recursive: true});
}

const tempDir = getStoreValue(store, 'tempDir') as string;
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, {recursive: true});
}

// Khởi tạo VoiceStore
const voiceStore = getVoiceStore();

// Khởi tạo WindowManager
const windowManager = WindowManager.getInstance();

// Khởi tạo FinetuneService
const finetuneService = FinetuneService.getInstance();

// Khởi tạo SpeechService
const speechService = SpeechService.getInstance();
const ttsQueue = TTSQueue.getInstance();

// Khôi phục queue từ store khi app khởi động
(async () => {
  await ttsQueue.restoreQueueFromStore();
})();

// Hàm lấy thời lượng audio sử dụng ffprobe
function getAudioDuration(filePath: string): number {
  try {
    // Sử dụng ffprobe để lấy thông tin về file audio
    const command = `ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "${filePath}"`;
    const output = execSync(command).toString().trim();
    return parseFloat(output);
  } catch (error) {
    console.error(`Error getting audio duration for ${filePath}:`, error);
    return 0;
  }
}

let mainWindow: BrowserWindow | null = null;

// Tạo cửa sổ khi app sẵn sàng
app.whenReady().then(async () => {
  // await setupPython();
  // Đăng ký protocol local
  protocol.registerFileProtocol('local', (request, callback) => {
    const url = request.url.substr(7);
    try {
      return callback(decodeURIComponent(url));
    } catch (error) {
      console.error('ERROR: registerLocalProtocol: Could not get file path:', error);
    }
  });

  // Tạo cửa sổ chính
  windowManager.createWindow();

  // Ghi log khởi động ứng dụng
  logger.info('Gen Voice đã khởi động', {version: app.getVersion()}, 'system');

  app.on('activate', () => windowManager.showWindow());
});

// Đóng app khi tất cả cửa sổ đóng (trừ macOS)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

// IPC handlers
ipcMain.handle('getStoreValue', (_, key) => getStoreValue(store, key));

ipcMain.handle('setStoreValue', (_, key, value) => {
  setStoreValue(store, key, value);
  return true;
});

ipcMain.handle('selectFolder', async () => {
  const result = await dialog.showOpenDialog(mainWindow!, {
    properties: ['openDirectory']
  });

  if (!result.canceled) {
    return result.filePaths[0];
  }
  return null;
});

ipcMain.handle('selectVoiceZip', async () => {
  const result = await dialog.showOpenDialog({
    properties: ['openFile'],
    filters: [{name: 'ZIP Files', extensions: ['zip']}]
  });

  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths[0];
  }
  return null;
});

ipcMain.handle('selectExportPath', async (_, defaultPath) => {
  const result = await dialog.showSaveDialog({
    defaultPath: defaultPath || path.join(app.getPath('downloads'), 'voice.zip'),
    filters: [{name: 'ZIP Files', extensions: ['zip']}]
  });

  if (!result.canceled && result.filePath) {
    return result.filePath;
  }
  return null;
});

ipcMain.handle('getAudioFiles', async (_, folderPath) => {
  try {
    const files = fs.readdirSync(folderPath);
    const audioFiles = files.filter(file =>
        file.endsWith('.wav') || file.endsWith('.mp3') ||
        file.endsWith('.flac') || file.endsWith('.ogg') ||
        file.endsWith('.m4a'));
    const result = [];

    for (const audioFile of audioFiles) {
      const extension = path.extname(audioFile);
      const baseName = path.basename(audioFile, extension);
      const textFilePath = path.join(folderPath, `${baseName}.txt`);
      const audioFilePath = path.join(folderPath, audioFile);

      if (fs.existsSync(textFilePath)) {
        const text = fs.readFileSync(textFilePath, 'utf-8');
        let duration = 0;
        try {
          duration = getAudioDuration(audioFilePath);
          duration = Math.round(duration * 10) / 10;
        } catch (err) {
          console.error(`Error getting audio duration for ${audioFile}:`, err);
          try {
            const stats = fs.statSync(audioFilePath);
            const fileSize = stats.size;
            duration = Math.round((fileSize / 88200) * 10) / 10;
          } catch (statErr) {
            console.error(`Fallback method also failed for ${audioFile}:`, statErr);
          }
        }

        result.push({
          audioPath: audioFilePath,
          textPath: textFilePath,
          text: text,
          fileName: baseName,
          duration: duration
        });
      }
    }

    return result;
  } catch (error) {
    console.error('Error reading audio files:', error);
    return [];
  }
});

// Voice operations
ipcMain.handle('getVoices', () => voiceStore.getVoices());

ipcMain.handle('getVoice', (_, id) => voiceStore.getVoice(id));

ipcMain.handle('addVoice', (_, voice) => {
  voiceStore.addVoice(voice);
  return true;
});

ipcMain.handle('updateVoice', async (_, voice: Voice) => await voiceStore.updateVoice(voice));

ipcMain.handle('deleteVoice', async (_, voiceId: string) => await voiceStore.deleteVoice(voiceId));

ipcMain.handle('setDefaultVoice', (_, id) => {
  voiceStore.setDefaultVoice(id);
  return true;
});

ipcMain.handle('getDefaultVoice', () => voiceStore.getDefaultVoice());

ipcMain.handle('importVoice', async (_, params: { importDir: string; voiceName?: string }) => await voiceStore.importVoice(params.importDir, params.voiceName));

ipcMain.handle('exportVoice', async (_, id, outputPath) => await voiceStore.exportVoice(id, outputPath));

// TTS operations
ipcMain.handle('textToSpeech', async (_, params: TextToSpeechParams) => {
  try {
    const processId = params.processId;

    const task: TTSQueueItem = {
      processId,
      execute: async () => {
        await speechService.textToSpeech(params);
      }
    };

    await ttsQueue.addTask(task);
  } catch (error) {
    logger.error('Error occurred in handler for \'textToSpeech\':', error);
  }
});

// Metadata operations
ipcMain.handle('processMetadata', async (_, params) => {
  try {
    return await finetuneService.processMetadata({
      ...params,
      voicesPath: getStoreValue(store, 'voicesPath')
    });
  } catch (error) {
    console.error('Error processing metadata:', error);
    return {error: (error as Error).message};
  }
});

// Finetune operations
ipcMain.handle('startFinetune', async (_, {voiceName, trainMetadataPath, evalMetadataPath, epochs = 10, testText}) => {
  return finetuneService.startFinetune({
    voiceName,
    outputFolder: path.join(getStoreValue(store, 'voicesPath'), voiceName),
    trainMetadataPath,
    evalMetadataPath,
    epochs,
    testText
  });
});

ipcMain.handle('cancelFinetune', (_, processId) => killPythonProcess(processId));

ipcMain.handle('getFinetuneStatus', (_, processId) => getPythonProcessInfo(processId));

// Logger operations
ipcMain.handle('loggerLog', (_, level, message, data, channel) => {
  logger.log(level as LogLevel, message, data, channel as LogChannel);
  return true;
});

ipcMain.handle('getLogs', (_, filter) => {
  return loggerCore.getLogs(filter);
});

ipcMain.handle('clearLogs', () => {
  loggerCore.clearLogs();
  return true;
});

ipcMain.handle('getLogConfig', () => {
  return loggerCore.getConfig();
});

ipcMain.handle('updateLogConfig', (_, config) => {
  loggerCore.updateConfig(config);
  return true;
});

// Toast operations
ipcMain.handle('showToast', (_, {type, message}) => {
  windowManager.sendToRenderer('toast', {type, message});
  return true;
});

ipcMain.handle('updateProcess', async (_event, process) => {
  try {
    const store = TTSProcessStore.getInstance();
    store.updateProcess(process);
    return true;
  } catch (error) {
    console.error('Error updating process:', error);
    throw error;
  }
});

ipcMain.handle('removeProcess', async (_event, processId) => {
  try {
    const store = TTSProcessStore.getInstance();
    await store.deleteProcess(processId);
    return true;
  } catch (error) {
    console.error('Error removing process:', error);
    throw error;
  }
});

ipcMain.handle('getTTSProcesses', async () => {
  try {
    const store = TTSProcessStore.getInstance();
    return store.getProcesses();
  } catch (error) {
    console.error('Error getting TTS processes:', error);
    throw error;
  }
});

ipcMain.handle('cleanupCompletedProcesses', async () => {
  try {
    const store = TTSProcessStore.getInstance();
    return await store.cleanupCompletedProcesses();
  } catch (error) {
    console.error('Error cleaning up completed processes:', error);
    throw error;
  }
});

ipcMain.handle('isExistFile', async (_event, filePath: string) => {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    console.error('Error checking file existence:', error);
    return false;
  }
});

ipcMain.handle('createProcess', async (_event, process: TTSProcess) => {
  try {
    const store = TTSProcessStore.getInstance();
    store.createProcess(process);
    return true;
  } catch (error) {
    console.error('Error creating process:', error);
    throw error;
  }
});

ipcMain.handle('cancelProcess', async (_event, processId) => {
  try {
    console.log('[cancelProcess] Gọi killPythonProcess với processId:', processId);
    const result = killPythonProcess(processId);
    if (!result) {
      console.warn('[cancelProcess] Không tìm thấy processId trong runningProcesses:', processId);
    }
    return result;
  } catch (error) {
    console.error('Error cancelling process:', error);
    return false;
  }
});

ipcMain.handle('selectFiles', async (_, options) => {
  try {
    const result = await dialog.showOpenDialog({
      filters: options.filters,
      properties: options.properties
    });
    return result.filePaths;
  } catch (error) {
    console.error('Error selecting files:', error);
    throw error;
  }
});

// Open userData folder
ipcMain.handle('openUserDataFolder', async () => {
  try {
    const userDataPath = app.getPath('userData');
    await shell.openPath(userDataPath);
    return true;
  } catch (error) {
    console.error('Error opening userData folder:', error);
    return false;
  }
});

ipcMain.handle('reloadChunk', async (_, params: ReloadChunk) => {
  try {
    const result = await speechService.reloadChunk(params);

    if (result.error) {
      logger.error('SpeechService reloadChunk error:', result.error);
      return { error: result.error };
    }
    logger.info('Chunk reloaded successfully', { processId: params.processId, chunkId: params.chunkId });
    return { success: true };
  } catch (error) {
    logger.error('Error reloading chunk:', error);
    return { error: error instanceof Error ? error.message : String(error) };
  }
});

ipcMain.handle('getAudioDuration', async (_, audioPath: string, processId: string) => {
  try {
    return await speechService.getAudioDuration(audioPath, processId);
  } catch (error) {
    logger.error('Error getting audio duration:', error);
    throw error;
  }
});

ipcMain.handle('reorderProcesses', async (_event, newOrder: string[]) => {
  try {
    await ttsQueue.reorderQueue(newOrder);
  } catch (error) {
    console.error('Error reordering processes:', error);
    throw error;
  }
});
