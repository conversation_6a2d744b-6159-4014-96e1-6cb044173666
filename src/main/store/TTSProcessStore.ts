import type {default as Store} from 'electron-store';
import type {TTSProcess, TTSProcessStoreInterface} from '@types';
import {logger} from '@main/logger/client';
import {createStore, getStoreValue, setStoreValue} from '@main/utils/store-helper';
import * as fs from 'fs';
import * as path from 'path';

export class TTSProcessStore {
  private static instance: TTSProcessStore;
  private store: Store<TTSProcessStoreInterface>;

  private constructor() {
    this.store = createStore<TTSProcessStoreInterface>({
      name: 'tts_processes',
      defaults: {
        processes: [],
      },
    });
  }

  public static getInstance(): TTSProcessStore {
    if (!TTSProcessStore.instance) {
      TTSProcessStore.instance = new TTSProcessStore();
    }
    return TTSProcessStore.instance;
  }

  // L<PERSON>y tất cả processes
  public getProcesses(): TTSProcess[] {
    return this.store.get('processes');
  }

  // Lấy process theo id
  public getProcess(processId: string): TTSProcess | undefined {
    const processes = this.store.get('processes');
    return processes?.find((process) => process.id === processId) || undefined;
  }

  // Thêm process mới
  public createProcess(process: TTSProcess): void {
    const processes = this.store.get('processes');
    if (processes) {
      processes.push(process);
      this.store.set('processes', processes);
    }
  }

  // Cập nhật process
  public updateProcess(updatedProcess: TTSProcess): TTSProcess {
    try {
      logger.info(`Update TTSProcess: ${updatedProcess.id}`);
      const processes = this.store.get('processes');
      const index = processes?.findIndex((p) => p.id === updatedProcess.id);

      if (index === -1) {
        logger.error(`Process not found by id: ${updatedProcess.id}`);
        throw new Error('Process not found');
      }

      // Cập nhật process trong store
      if (processes) {
        updatedProcess.updatedAt = new Date().toISOString();
        processes[index] = updatedProcess;
        this.store.set('processes', processes);
        logger.info(`TTSProcess updated: ${updatedProcess.id}`);
        return updatedProcess;
      }

    } catch (error) {
      logger.error('Update TTSProcess ERROR:', error);
      throw error;
    }
  }

  /**
   * Xóa các file chunk audio và thư mục liên quan
   * @param process Process cần xóa file
   */
  private async cleanupProcessFiles(process: TTSProcess): Promise<void> {
    try {
      // Xóa các chunk audio files
      if (process.chunks && process.chunks.length > 0) {
        for (const chunk of process.chunks) {
          if (chunk.audioPath && fs.existsSync(chunk.audioPath)) {
            try {
              fs.unlinkSync(chunk.audioPath);
            } catch (error) {
              logger.warning(`Không thể xóa chunk audio file: ${chunk.audioPath}`, error);
            }
          }
        }

        // Xóa thư mục chunks nếu có
        if (process.chunks[0]?.audioPath) {
          const chunksDir = path.dirname(process.chunks[0].audioPath);
          if (fs.existsSync(chunksDir)) {
            try {
              // Kiểm tra xem thư mục có rỗng không trước khi xóa
              const files = fs.readdirSync(chunksDir);
              if (files.length === 0) {
                fs.rmdirSync(chunksDir);
              }
            } catch (error) {
              logger.warning(`Không thể xóa thư mục chunks: ${chunksDir}`, error);
            }
          }
        }
      }
    } catch (error) {
      logger.error('Lỗi khi dọn dẹp file process:', error);
    }
  }

  // Xóa process
  public async deleteProcess(id: string): Promise<void> {
    try {
      const processes = this.store.get('processes');
      const process = processes?.find((p) => p.id === id);

      if (!process) {
        logger.error(`Không tìm thấy process với id ${id}`);
        return;
      }

      // Xóa các file liên quan
      await this.cleanupProcessFiles(process);

      // Xóa process khỏi danh sách
      const filteredProcesses = processes?.filter((p) => p.id !== id);
      if (filteredProcesses) {
        this.store.set('processes', filteredProcesses);
      }

      logger.info(`Đã xóa process thành công: ${id}`);
    } catch (error) {
      logger.error('Lỗi khi xóa process:', error);
      throw error;
    }
  }

  /**
   * Xóa hàng loạt các process đã hoàn thành/thất bại/bị hủy
   * @returns Danh sách process còn lại sau khi cleanup
   */
  public async cleanupCompletedProcesses(): Promise<TTSProcess[]> {
    try {
      const processes = this.store.get('processes');

      // Tìm các process cần xóa
      const processesToCleanup = processes?.filter(p =>
        p.status === 'completed' || p.status === 'failed' || p.status === 'cancelled'
      );

      // Tìm các process còn lại
      const remainingProcesses = processes?.filter(p =>
        p.status !== 'completed' && p.status !== 'failed' && p.status !== 'cancelled'
      );

      logger.info(`Bắt đầu cleanup ${processesToCleanup?.length} processes`);

      // Xóa file của từng process
      for (const process of processesToCleanup || []) {
        try {
          await this.cleanupProcessFiles(process);
        } catch (error) {
          logger.warning(`Lỗi khi cleanup files cho process ${process.id}:`, error);
        }
      }

      // Cập nhật store với danh sách process còn lại
      if (remainingProcesses) {
        this.store.set('processes', remainingProcesses);
      }

      logger.info(`Đã cleanup thành công ${processesToCleanup?.length} processes, còn lại ${remainingProcesses?.length} processes`);

      return remainingProcesses || [];
    } catch (error) {
      logger.error('Lỗi khi cleanup processes:', error);
      throw error;
    }
  }

  public setProcesses(processes: TTSProcess[]): void {
    this.store.set('processes', processes);
  }
}